"""
Task Orchestrator for the VPS Admin system.
The central "brain" that manages the overall state and coordinates between agents.
"""

import asyncio
import json
import traceback
from typing import AsyncGenerator

from sse_starlette.sse import ServerSentEvent

from config import Config
from models import (
    TaskEntry, TaskPlan, TaskStep, StepStatus, StepAttempt, SSHResult
)
from agents import PlannerAgent, ExecutorAgent, RefinerAgent, SummarizerAgent
from ssh_client import SSHClient
from ai_client import AIClient


class TaskOrchestrator:
    """Central orchestrator that manages task execution using specialized agents."""

    def __init__(self, config: Config, ssh_client: SSHClient):
        self.config = config
        self.ssh_client = ssh_client

        # Initialize specialized agents
        self.planner = PlannerAgent(config)
        self.executor = ExecutorAgent(config)
        self.refiner = RefinerAgent(config)
        self.summarizer = SummarizerAgent(config)
        # Note: ErrorRecoveryPlanner removed - using RefinerAgent for consistent behavior

        # Initialize AI client for intent detection
        self.ai_client = AIClient(config)

        print("TaskOrchestrator initialized with specialized agents")

    async def execute_task(self, task: TaskEntry, user_message: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Main orchestrator execution loop with intent detection."""
        print(f"[Orchestrator {task.id}] Starting task execution with message: '{user_message[:100]}...'")

        try:
            # Phase 0: Intent Detection (for new tasks only)
            if not task.task_plan or not task.task_plan.steps:
                # Detect user intent using Gemma-27b
                print(f"[Orchestrator {task.id}] Detecting user intent...")

                # Get intent, percentage, and potential chat response
                intent_type, percentage, chat_response = await self.ai_client._detect_user_intent_with_percentage(
                    user_message,
                    task.main_context_history_string if hasattr(task, 'main_context_history_string') else ""
                )

                print(f"[Orchestrator {task.id}] Intent detected: {intent_type}, Percentage: {percentage}%, Chat response: {bool(chat_response)}")

                # Handle chat intent directly
                if intent_type == "chat" and chat_response:
                    async for event in self._handle_chat_response(task, user_message, chat_response):
                        yield event
                    return

                # For VPS tasks, store percentage for planning phase
                task.task_difficulty_percentage = percentage

            # Phase 1: Planning (if no plan exists)
            if not task.task_plan or not task.task_plan.steps:
                async for event in self._planning_phase(task, user_message):
                    yield event

                # Check if planning succeeded
                if task.status == "FAILED":
                    print(f"[Orchestrator {task.id}] Planning failed, aborting execution")
                    return
                elif task.status != "PLANNED":
                    print(f"[Orchestrator {task.id}] Planning did not complete properly (status: {task.status}), aborting execution")
                    return

            # Phase 2: Execution Loop
            async for event in self._execution_phase(task):
                yield event

        except Exception as e:
            error_msg = f"Orchestrator error: {type(e).__name__}: {e}"
            print(f"[Orchestrator {task.id}] CRITICAL ERROR: {error_msg}\n{traceback.format_exc()}")

            task.status = "FAILED"
            task.final_output = error_msg

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error",
                    "content": error_msg,
                    "metadata": {"category": "orchestrator_error"}
                }),
                event="error"
            )

    async def _handle_chat_response(self, task: TaskEntry, user_message: str, chat_response: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle chat responses directly without planning/execution."""
        print(f"[Orchestrator {task.id}] Handling chat response")

        # Update task status
        task.status = "COMPLETED"
        task.final_output = chat_response

        # Update chat history
        self.ai_client.update_chat_history(task.chat, user_message, chat_response)

        # Send chat response to frontend
        yield ServerSentEvent(
            data=json.dumps({
                "type": "ai_response",
                "content": chat_response,
                "metadata": {
                    "category": "chat_response",
                    "intent_type": "chat",
                    "percentage": 0
                }
            }),
            event="message"
        )

        # Send task completion
        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_end",
                "content": "Chat response completed",
                "metadata": {"category": "task_management"}
            }),
            event="end"
        )

    async def _planning_phase(self, task: TaskEntry, user_prompt: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Phase 1: Break down the user request into a structured plan."""
        print(f"[Orchestrator {task.id}] Entering planning phase")

        task.status = "PLANNING"
        task.original_prompt = user_prompt

        yield ServerSentEvent(
            data=json.dumps({
                "type": "progress",
                "content": "Analyzing your request and creating a plan...",
                "metadata": {"phase": "planning", "category": "task_management"}
            }),
            event="message"
        )

        try:
            # Get task difficulty percentage for thinking budget
            percentage = getattr(task, 'task_difficulty_percentage', 70)  # Default to 70% if not set

            print(f"[Orchestrator {task.id}] Starting planner with {percentage}% difficulty")

            # Call the Planner Agent with percentage for thinking budget and timeout
            planner_response = await asyncio.wait_for(
                self.planner.process(
                    user_prompt=user_prompt,
                    system_info=task.system_info,
                    percentage=percentage
                ),
                timeout=180  # 3 minutes timeout for planning
            )

            print(f"[Orchestrator {task.id}] Planner completed. Success: {planner_response.success}, Steps: {len(planner_response.plan_steps) if planner_response.plan_steps else 0}")

            if not planner_response.plan_steps:
                # No plan steps at all - this is a critical failure
                error_details = planner_response.metadata.get('error', 'Unknown error')
                raise ValueError(f"Planner failed to create any plan steps: {error_details}")

            if not planner_response.success:
                # Planner had issues but created a fallback plan - log warning but continue
                error_details = planner_response.metadata.get('error', 'Unknown error')
                print(f"[Orchestrator {task.id}] WARNING: Planner used fallback plan due to: {error_details}")

                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "warning",
                        "content": f"Using simplified plan due to planning issues: {error_details}",
                        "metadata": {"phase": "planning", "category": "planning_warning"}
                    }),
                    event="message"
                )

        except asyncio.TimeoutError:
            print(f"[Orchestrator {task.id}] ERROR: Planning timed out after 3 minutes")
            task.status = "FAILED"
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error",
                    "content": "Planning timed out. Please try again with a simpler request.",
                    "metadata": {"phase": "planning", "category": "planning_error"}
                }),
                event="error"
            )
            return

        # Create TaskPlan from planner response
            steps = []
            for i, step_data in enumerate(planner_response.plan_steps, 1):
                step = TaskStep(
                    step_number=i,
                    description=step_data['description'],
                    command=step_data.get('command'),  # Include command from planner
                    status=StepStatus.PENDING,
                    created_at=asyncio.get_event_loop().time()
                )
                steps.append(step)

            task.task_plan = TaskPlan(
                steps=steps,
                total_steps=len(steps),
                current_step=0
            )

            # Update legacy fields for backward compatibility
            task.estimated_steps = len(steps)
            task.current_step = 0

            print(f"[Orchestrator {task.id}] Plan created with {len(steps)} steps")

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "plan_created",
                    "content": f"Plan created with {len(steps)} steps",
                    "metadata": {
                        "phase": "planning",
                        "total_steps": len(steps),
                        "steps": [
                            {
                                "description": step.description,
                                "command": step.command
                            } for step in steps
                        ],
                        "category": "task_management"
                    }
                }),
                event="message"
            )

            # Mark planning as completed successfully
            task.status = "PLANNED"
            print(f"[Orchestrator {task.id}] Planning phase completed successfully")

        except Exception as e:
            error_msg = f"Planning failed: {str(e)}"
            print(f"[Orchestrator {task.id}] ERROR in planning: {error_msg}")

            task.status = "FAILED"
            task.final_output = error_msg

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error",
                    "content": error_msg,
                    "metadata": {"phase": "planning", "category": "planning_error"}
                }),
                event="error"
            )
            return

    async def _execution_phase(self, task: TaskEntry) -> AsyncGenerator[ServerSentEvent, None]:
        """Phase 2: Execute the plan step by step."""
        print(f"[Orchestrator {task.id}] Entering execution phase")

        task.status = "EXECUTING"

        if not task.task_plan or not task.task_plan.steps:
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error",
                    "content": "No execution plan available",
                    "metadata": {"phase": "execution", "category": "execution_error"}
                }),
                event="error"
            )
            return

        total_steps = len(task.task_plan.steps)

        # Execute each step
        for step_index, step in enumerate(task.task_plan.steps):
            if step.status == StepStatus.COMPLETED:
                continue  # Skip already completed steps

            task.task_plan.current_step = step_index
            task.current_step = step_index + 1  # Legacy field (1-based)

            # CRITICAL FIX: Clear step attempts when starting a new step
            task.current_step_attempts = []

            print(f"[Orchestrator {task.id}] Executing step {step_index + 1}/{total_steps}: {step.description}")

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "step_start",
                    "content": f"Step {step_index + 1}/{total_steps}: {step.description}",
                    "metadata": {
                        "phase": "execution",
                        "step_number": step_index + 1,
                        "total_steps": total_steps,
                        "step_description": step.description,
                        "category": "step_execution"
                    }
                }),
                event="message"
            )

            # Execute the current step (this will request confirmation and return)
            async for event in self._execute_single_step(task, step):
                yield event

            # If we're awaiting confirmation, stop here and wait for user response
            if task.awaiting_step_confirmation:
                print(f"[Orchestrator {task.id}] Awaiting user confirmation for step {step_index + 1}")
                return

            # Check if step completed successfully
            if step.status == StepStatus.COMPLETED:
                print(f"[Orchestrator {task.id}] Step {step_index + 1} completed successfully")

                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "step_complete",
                        "content": f"Step {step_index + 1} completed: {step.summary or step.description}",
                        "metadata": {
                            "phase": "execution",
                            "step_number": step_index + 1,
                            "total_steps": total_steps,
                            "category": "step_completion"
                        }
                    }),
                    event="message"
                )

                # Clear current step attempts for next step
                task.current_step_attempts = []

            elif step.status == StepStatus.FAILED:
                print(f"[Orchestrator {task.id}] Step {step_index + 1} failed")

                # Task failed
                task.status = "FAILED"
                task.final_output = f"Task failed at step {step_index + 1}: {step.description}"

                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "task_failed",
                        "content": f"Task failed at step {step_index + 1}. Could not complete: {step.description}",
                        "metadata": {
                            "phase": "execution",
                            "failed_step": step_index + 1,
                            "total_steps": total_steps,
                            "category": "task_failure"
                        }
                    }),
                    event="error"
                )
                return

        # All steps completed successfully
        task.status = "COMPLETED"
        task.final_output = "Task completed successfully"

        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_complete",
                "content": "All steps completed successfully!",
                "metadata": {
                    "phase": "completion",
                    "total_steps": total_steps,
                    "category": "task_completion"
                }
            }),
            event="message"
        )

        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_end",
                "content": "Task completed successfully",
                "metadata": {"category": "task_management"}
            }),
            event="end"
        )

    async def _execute_single_step(self, task: TaskEntry, step: TaskStep, start_attempt: int = 1) -> AsyncGenerator[ServerSentEvent, None]:
        """Execute a single step with retry logic and user confirmation."""
        step.status = StepStatus.EXECUTING
        max_retries = task.max_retries_per_step

        # Start from the specified attempt number (for retry scenarios)
        for attempt_num in range(start_attempt, max_retries + 1):
            print(f"[Orchestrator {task.id}] Step {step.step_number} attempt {attempt_num}/{max_retries}")

            try:
                # Get command - use planned command or generate one
                if attempt_num == 1 and hasattr(step, 'command') and step.command:
                    # First attempt: use command from plan
                    command = step.command
                    print(f"[Orchestrator {task.id}] Using planned command: {command}")
                elif attempt_num == 1:
                    # First attempt but no planned command: use Executor Agent
                    executor_response = await self.executor.process(
                        step_description=step.description,
                        context_history=task.main_context_history,
                        system_info=task.system_info
                    )

                    if not executor_response.success:
                        raise ValueError(f"Executor failed: {executor_response.content}")

                    command = executor_response.command
                else:
                    # Retry attempt: use Refiner Agent
                    refiner_response = await self.refiner.process(
                        step_description=step.description,
                        failed_attempts=[
                            {
                                "command": attempt.command,
                                "error_message": attempt.error_message,
                                "stdout": attempt.stdout,
                                "stderr": attempt.stderr,
                                "exit_status": attempt.exit_status
                            }
                            for attempt in task.current_step_attempts
                        ]
                    )

                    if not refiner_response.success:
                        raise ValueError(f"Refiner failed: {refiner_response.content}")

                    command = refiner_response.corrected_command

                # Request user confirmation before executing
                print(f"[Orchestrator {task.id}] Requesting confirmation for command: {command}")

                # Set confirmation state
                task.awaiting_step_confirmation = True
                task.current_step_command = command
                task.current_step_description = step.description
                task.current_step_number = step.step_number
                task.current_step_attempt = attempt_num  # Track current attempt number
                task.status = "AWAITING_USER_CONFIRMATION"

                # Send confirmation request to frontend
                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "command_confirmation",
                        "content": command,
                        "metadata": {
                            "category": "system_administration",
                            "priority": "medium",
                            "stepNumber": step.step_number,
                            "totalSteps": len(task.task_plan.steps) if task.task_plan else task.estimated_steps,
                            "stepDescription": step.description,
                            "securityRisk": self._assess_security_risk(command),
                            "attemptNumber": attempt_num,
                            "maxAttempts": max_retries
                        }
                    }),
                    event="message"
                )

                # Return here - execution will continue when user confirms
                return

            except Exception as e:
                error_msg = f"Step execution error: {str(e)}"
                print(f"[Orchestrator {task.id}] ERROR in step execution: {error_msg}")

                # Send error event
                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "error",
                        "content": f"Step {step.step_number} failed: {error_msg}",
                        "metadata": {
                            "category": "execution_error",
                            "stepNumber": step.step_number,
                            "attemptNumber": attempt_num
                        }
                    }),
                    event="message"
                )

                # Record failed attempt
                attempt = StepAttempt(
                    attempt_number=attempt_num,
                    command=command if 'command' in locals() else "unknown",
                    error_message=error_msg,
                    stdout="",
                    stderr=error_msg,
                    exit_status=-1,
                    timestamp=asyncio.get_event_loop().time()
                )
                task.current_step_attempts.append(attempt)

                if attempt_num >= max_retries:
                    step.status = StepStatus.FAILED
                    return

                # Continue to next retry
                continue

    async def _execute_confirmed_command(self, task: TaskEntry, command: str, step: TaskStep, attempt_num: int) -> AsyncGenerator[ServerSentEvent, None]:
        """Execute a command that has been confirmed by the user."""
        print(f"[Orchestrator {task.id}] Executing confirmed command: {command}")

        # Execute the command
        ssh_result = await self.ssh_client.execute_command_async(command)

        # Send execution result
        yield ServerSentEvent(
            data=json.dumps({
                "type": "command_result",
                "content": f"Command executed: {command}",
                "metadata": {
                    "success": ssh_result.success,
                    "exit_status": ssh_result.exit_status,
                    "stdout": ssh_result.stdout,
                    "stderr": ssh_result.stderr,
                    "execution_time": ssh_result.execution_time,
                    "stepNumber": step.step_number,
                    "attemptNumber": attempt_num
                }
            }),
            event="message"
        )

        # Record the attempt
        attempt = StepAttempt(
            attempt_number=attempt_num,
            command=command,
            ssh_result=ssh_result,
            error_message=ssh_result.stderr if not ssh_result.success else None,
            stdout=ssh_result.stdout,
            stderr=ssh_result.stderr,
            exit_status=ssh_result.exit_status,
            timestamp=asyncio.get_event_loop().time(),
            duration_ms=ssh_result.execution_time
        )
        task.current_step_attempts.append(attempt)

        # Update legacy tracking
        task.commands_executed += 1
        if ssh_result.success:
            task.commands_successful += 1
        else:
            task.commands_failed += 1

        if ssh_result.success:
            # Command succeeded - create summary
            try:
                summarizer_response = await self.summarizer.process(
                    step_description=step.description,
                    command=command,
                    ssh_result=ssh_result
                )

                step.summary = summarizer_response.step_summary
                step.command = command
                step.status = StepStatus.COMPLETED

                # Add to main context history
                task.main_context_history.append(
                    f"Step {step.step_number}: {summarizer_response.step_summary}"
                )

                # Send step completion event
                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "step_completed",
                        "content": f"Step {step.step_number} completed: {step.summary}",
                        "metadata": {
                            "stepNumber": step.step_number,
                            "summary": step.summary,
                            "category": "step_completion"
                        }
                    }),
                    event="message"
                )

            except Exception as summary_error:
                print(f"[Orchestrator {task.id}] WARNING: Summary generation failed: {summary_error}")
                step.summary = f"Completed: {step.description}"
                step.command = command
                step.status = StepStatus.COMPLETED
                task.main_context_history.append(f"Step {step.step_number}: {step.summary}")

        else:
            # Command failed - check if we should trigger error recovery
            print(f"[Orchestrator {task.id}] Command failed (attempt {attempt_num}): {ssh_result.stderr}")
            print(f"[Orchestrator {task.id}] DEBUG: attempt_num={attempt_num}, max_retries={task.max_retries_per_step}, should_trigger_recovery={self._should_trigger_error_recovery(ssh_result)}")

            max_retries = task.max_retries_per_step

            # Check if this is the last attempt - use RefinerAgent for consistent behavior
            if attempt_num >= max_retries and self._should_trigger_error_recovery(ssh_result):
                print(f"[Orchestrator {task.id}] ✅ USING REFINER AGENT for failed command (attempt {attempt_num}/{max_retries}) - consistent behavior")

                # Use RefinerAgent for consistent direct command proposal (like step 1)
                # This ensures the same behavior as early retries instead of switching to recovery plans
                try:
                    refiner_response = await self.refiner.process(
                        step_description=step.description,
                        failed_attempts=[
                            {
                                "command": attempt.command,
                                "error_message": attempt.error_message,
                                "stdout": attempt.stdout,
                                "stderr": attempt.stderr,
                                "exit_status": attempt.exit_status
                            }
                            for attempt in task.current_step_attempts
                        ]
                    )

                    if refiner_response.success:
                        # Directly propose the new command (same as step 1 behavior)
                        new_command = refiner_response.corrected_command

                        print(f"[Orchestrator {task.id}] RefinerAgent proposed alternative: {new_command}")

                        # Request user confirmation for the new approach
                        task.awaiting_step_confirmation = True
                        task.current_step_command = new_command
                        task.current_step_description = step.description
                        task.current_step_number = step.step_number
                        task.current_step_attempt = attempt_num + 1  # Next attempt
                        task.status = "AWAITING_USER_CONFIRMATION"

                        # Send confirmation request to frontend
                        yield ServerSentEvent(
                            data=json.dumps({
                                "type": "command_confirmation",
                                "content": new_command,
                                "metadata": {
                                    "category": "system_administration",
                                    "priority": "medium",
                                    "stepNumber": step.step_number,
                                    "totalSteps": len(task.task_plan.steps) if task.task_plan else task.estimated_steps,
                                    "stepDescription": step.description,
                                    "securityRisk": self._assess_security_risk(new_command),
                                    "attemptNumber": attempt_num + 1,
                                    "maxAttempts": max_retries,
                                    "isAlternativeApproach": True
                                }
                            }),
                            event="message"
                        )
                        return
                    else:
                        print(f"[Orchestrator {task.id}] RefinerAgent failed: {refiner_response.content}")
                        # Fall through to step failure handling

                except Exception as refiner_error:
                    print(f"[Orchestrator {task.id}] RefinerAgent error: {refiner_error}")
                    # Fall through to step failure handling

            # Check if we should skip this step due to excessive attempts
            elif self._should_skip_step_after_max_attempts(task, step):
                print(f"[Orchestrator {task.id}] Step {step.step_number} exceeded maximum attempts, offering skip option")

                # Offer user the option to skip this step
                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "step_skip_option",
                        "content": f"Step {step.step_number} has failed multiple times. Would you like to skip this step and continue?",
                        "metadata": {
                            "stepNumber": step.step_number,
                            "totalAttempts": len(task.current_step_attempts),
                            "category": "step_management",
                            "options": ["skip_step", "retry_step", "abort_task"]
                        }
                    }),
                    event="user_choice"
                )

                # Set task to await user decision
                task.status = "AWAITING_SKIP_DECISION"
                task.awaiting_step_confirmation = True
                return

            # Mark step as failed if max retries reached without recovery
            elif attempt_num >= max_retries:
                step.status = StepStatus.FAILED
                print(f"[Orchestrator {task.id}] Step {step.step_number} failed after {max_retries} attempts")

                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "step_failed",
                        "content": f"Step {step.step_number} failed after {max_retries} attempts",
                        "metadata": {
                            "stepNumber": step.step_number,
                            "maxRetries": max_retries,
                            "category": "step_failure"
                        }
                    }),
                    event="message"
                )
            else:
                # More retries available - keep step in executing state for retry
                print(f"[Orchestrator {task.id}] Step {step.step_number} failed, {max_retries - attempt_num} retries remaining")
                step.status = StepStatus.EXECUTING

                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "step_retry",
                        "content": f"Step {step.step_number} failed, retrying... ({max_retries - attempt_num} attempts remaining)",
                        "metadata": {
                            "stepNumber": step.step_number,
                            "attemptNumber": attempt_num,
                            "retriesRemaining": max_retries - attempt_num,
                            "category": "step_retry"
                        }
                    }),
                    event="message"
                )

    def _assess_security_risk(self, command: str) -> bool:
        """Assess if a command poses security risks."""
        dangerous_patterns = [
            'rm -rf', 'dd if=', 'mkfs', 'fdisk', 'parted',
            'iptables -F', 'ufw --force', 'passwd', 'userdel',
            'chmod 777', 'chown -R root', '> /etc/', 'curl | sh',
            'wget | sh', 'eval', 'exec'
        ]
        return any(pattern in command.lower() for pattern in dangerous_patterns)

    async def handle_user_confirmation(self, task: TaskEntry, user_response: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle user confirmation response and continue execution."""
        if not task.awaiting_step_confirmation:
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error",
                    "content": "No command awaiting confirmation"
                }),
                event="error"
            )
            return

        command = task.current_step_command
        step_description = task.current_step_description
        step_number = task.current_step_number
        # FIX: Use the actual attempt count instead of the legacy field
        attempt_num = len(task.current_step_attempts) + 1  # This is the REAL attempt number

        if user_response.lower() == 'yes':
            print(f"[Orchestrator {task.id}] User confirmed command: {command} (attempt {attempt_num})")
            print(f"[Orchestrator {task.id}] DEBUG: current_step_attempts count: {len(task.current_step_attempts)}, max_retries: {task.max_retries_per_step}")
            print(f"[Orchestrator {task.id}] DEBUG: in_error_recovery={getattr(task, 'in_error_recovery', False)}")

            # Update the current step attempt to keep it in sync
            task.current_step_attempt = attempt_num

            # CRITICAL FIX: Always clear confirmation state FIRST to prevent stuck states
            task.awaiting_step_confirmation = False
            task.status = "EXECUTING"

            # Send immediate status update to frontend
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "status_update",
                    "content": "Executing command...",
                    "metadata": {
                        "status": "EXECUTING",
                        "category": "status_change"
                    }
                }),
                event="message"
            )

            # Check if we're in error recovery mode
            if task.in_error_recovery:
                print(f"[Orchestrator {task.id}] Executing recovery command: {command}")
                # Execute recovery command
                async for event in self._execute_recovery_command(task, command):
                    yield event
            else:
                # Normal step execution
                # Find the current step
                current_step = None
                if task.task_plan and task.task_plan.steps:
                    for step in task.task_plan.steps:
                        if step.step_number == step_number:
                            current_step = step
                            break

                if current_step:
                    # Execute the confirmed command with correct attempt number
                    async for event in self._execute_confirmed_command(task, command, current_step, attempt_num):
                        yield event

                    # Handle step completion or failure
                    if current_step.status == StepStatus.COMPLETED:
                        # Clear confirmation state and step attempts
                        task.current_step_command = None
                        task.current_step_description = None
                        task.current_step_number = 0
                        task.current_step_attempt = 1
                        task.current_step_attempts = []  # CRITICAL FIX: Clear step attempts after successful completion

                        # Continue execution from next step
                        async for event in self._continue_execution_after_confirmation(task):
                            yield event
                    elif current_step.status == StepStatus.EXECUTING:
                        # Command failed but step is still executing (retry needed)
                        # Continue with retry logic by calling _execute_single_step again
                        async for event in self._continue_step_retry(task, current_step):
                            yield event

        elif user_response.lower() in ['no', 'skip_step', 'skip']:
            if user_response.lower() in ['skip_step', 'skip']:
                print(f"[Orchestrator {task.id}] User chose to skip step")
                await self._handle_step_skip(task)
                return
            else:
                print(f"[Orchestrator {task.id}] User rejected command: {command}")

            # Clear confirmation state
            task.awaiting_step_confirmation = False
            task.current_step_command = None
            task.current_step_description = None
            task.current_step_number = 0
            task.current_step_attempt = 1

            # If we're in error recovery, abort recovery and fail the task
            if task.in_error_recovery:
                task.in_error_recovery = False
                task.recovery_plan = []
                task.current_recovery_step = 0

                # Mark the failed step as failed
                if task.task_plan and task.task_plan.steps:
                    for step in task.task_plan.steps:
                        if step.step_number == task.failed_step_number:
                            step.status = StepStatus.FAILED
                            break

                task.failed_step_number = 0
                task.status = "FAILED"

                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "error_recovery_aborted",
                        "content": "Error recovery aborted by user. Task failed.",
                        "metadata": {"category": "error_recovery"}
                    }),
                    event="message"
                )
            else:
                task.status = "ABORTED"

                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "task_aborted",
                        "content": "Task aborted by user - command execution rejected",
                        "metadata": {"category": "task_management"}
                    }),
                    event="message"
                )

            # Check current status to determine end message
            end_message = "Task failed - recovery aborted" if task.status == "FAILED" else "Task aborted by user"

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "task_end",
                    "content": end_message,
                    "metadata": {"category": "task_management"}
                }),
                event="end"
            )
        elif user_response.lower() in ['retry_step', 'retry']:
            print(f"[Orchestrator {task.id}] User chose to retry step")
            await self._handle_step_retry(task)
            return
        elif user_response.lower() in ['abort_task', 'abort']:
            print(f"[Orchestrator {task.id}] User chose to abort task")
            await self._handle_task_abort(task)
            return

    async def _continue_step_retry(self, task: TaskEntry, step: TaskStep) -> AsyncGenerator[ServerSentEvent, None]:
        """Continue with step retry after a failed command execution."""
        print(f"[Orchestrator {task.id}] Continuing step {step.step_number} retry logic")

        # Clear confirmation state but keep step executing
        # IMPORTANT: Do NOT reset current_step_attempt - this was causing the infinite loop bug!
        task.awaiting_step_confirmation = False
        task.current_step_command = None
        task.current_step_description = None
        task.current_step_number = 0
        # task.current_step_attempt = 1  # ❌ REMOVED: This was resetting attempt counter and preventing error recovery!
        task.status = "EXECUTING"

        # Calculate the next attempt number based on current step attempts
        next_attempt = len(task.current_step_attempts) + 1
        print(f"[Orchestrator {task.id}] Continuing from attempt {next_attempt} (previous attempts: {len(task.current_step_attempts)})")

        # Continue with the step execution (this will handle retries)
        async for event in self._execute_single_step(task, step, start_attempt=next_attempt):
            yield event

        # If we're awaiting confirmation again, stop here
        if task.awaiting_step_confirmation:
            return

        # Check final step status
        if step.status == StepStatus.FAILED:
            task.status = "FAILED"
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "task_failed",
                    "content": f"Task failed at step {step.step_number}",
                    "metadata": {"category": "task_failure"}
                }),
                event="error"
            )
            return
        elif step.status == StepStatus.COMPLETED:
            # Continue with next step
            async for event in self._continue_execution_after_confirmation(task):
                yield event
        elif step.status == StepStatus.EXECUTING:
            # Step is still executing - this means we're awaiting confirmation
            # Don't send the "yes/no" message as it's confusing
            print(f"[Orchestrator {task.id}] Step {step.step_number} still executing, awaiting confirmation")
        else:
            # Unexpected state - log it but don't confuse the user
            print(f"[Orchestrator {task.id}] WARNING: Step {step.step_number} in unexpected state: {step.status}")
            # Don't yield the confusing "yes/no" message

    async def _continue_execution_after_confirmation(self, task: TaskEntry) -> AsyncGenerator[ServerSentEvent, None]:
        """Continue execution after a step has been confirmed and completed."""
        if not task.task_plan or not task.task_plan.steps:
            return

        total_steps = len(task.task_plan.steps)

        # Find the next step to execute
        for step_index, step in enumerate(task.task_plan.steps):
            if step.status == StepStatus.COMPLETED:
                continue  # Skip completed steps

            # Execute this step
            task.task_plan.current_step = step_index
            task.current_step = step_index + 1

            # CRITICAL FIX: Clear step attempts when starting a new step
            task.current_step_attempts = []

            print(f"[Orchestrator {task.id}] Continuing with step {step_index + 1}/{total_steps}: {step.description}")

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "step_start",
                    "content": f"Step {step_index + 1}/{total_steps}: {step.description}",
                    "metadata": {
                        "phase": "execution",
                        "step_number": step_index + 1,
                        "total_steps": total_steps,
                        "step_description": step.description,
                        "category": "step_execution"
                    }
                }),
                event="message"
            )

            # Execute the step (this will request confirmation and return)
            async for event in self._execute_single_step(task, step):
                yield event

            # If we're awaiting confirmation, stop here
            if task.awaiting_step_confirmation:
                return

            # Check step status and continue or fail
            if step.status == StepStatus.FAILED:
                task.status = "FAILED"
                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "task_failed",
                        "content": f"Task failed at step {step_index + 1}",
                        "metadata": {"category": "task_failure"}
                    }),
                    event="error"
                )
                return

        # All steps completed
        task.status = "COMPLETED"
        task.final_output = "Task completed successfully"

        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_complete",
                "content": "All steps completed successfully!",
                "metadata": {
                    "phase": "completion",
                    "total_steps": total_steps,
                    "category": "task_completion"
                }
            }),
            event="message"
        )

        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_end",
                "content": "Task completed successfully",
                "metadata": {"category": "task_management"}
            }),
            event="end"
        )

    def _should_trigger_error_recovery(self, ssh_result: SSHResult) -> bool:
        """Determine if error recovery should be triggered based on the failure type."""
        # Simple and dynamic: trigger recovery for any non-zero exit code
        # This removes hardcoded patterns and makes it truly dynamic
        if ssh_result.exit_status == 0:
            return False  # Command succeeded, no recovery needed

        # Command failed (non-zero exit code) - trigger recovery
        # The AI will analyze the actual error and determine the best approach
        return True

    def _should_skip_step_after_max_attempts(self, task: TaskEntry, step: TaskStep) -> bool:
        """Determine if a step should be skipped after maximum attempts."""
        # Calculate total attempts for this step (including recovery attempts)
        total_attempts = len(task.current_step_attempts)

        # If we've exceeded reasonable limits, suggest skipping
        max_total_attempts = task.max_retries_per_step * 3  # Allow for recovery attempts

        if total_attempts >= max_total_attempts:
            print(f"[Orchestrator {task.id}] Step {step.step_number} has exceeded maximum total attempts ({total_attempts})")
            return True

        return False

    async def _handle_step_skip(self, task: TaskEntry) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle user request to skip the current step."""
        print(f"[Orchestrator {task.id}] Skipping current step")

        # Clear confirmation state
        task.awaiting_step_confirmation = False
        task.current_step_command = None
        task.current_step_description = None
        step_number = task.current_step_number
        task.current_step_number = 0
        task.current_step_attempt = 1

        # Find and mark the current step as skipped
        if task.task_plan and task.task_plan.steps:
            for step in task.task_plan.steps:
                if step.step_number == step_number:
                    step.status = StepStatus.SKIPPED
                    break

        # Clear current step attempts
        task.current_step_attempts = []

        yield ServerSentEvent(
            data=json.dumps({
                "type": "step_skipped",
                "content": f"Step {step_number} skipped by user",
                "metadata": {
                    "stepNumber": step_number,
                    "category": "step_management"
                }
            }),
            event="message"
        )

        # Continue with next step
        async for event in self._continue_execution_after_confirmation(task):
            yield event

    async def _handle_step_retry(self, task: TaskEntry) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle user request to retry the current step."""
        print(f"[Orchestrator {task.id}] Retrying current step")

        # Clear confirmation state but keep step info
        task.awaiting_step_confirmation = False
        task.current_step_command = None
        task.current_step_description = None
        step_number = task.current_step_number
        task.current_step_number = 0
        task.current_step_attempt = 1

        # Find the current step
        current_step = None
        if task.task_plan and task.task_plan.steps:
            for step in task.task_plan.steps:
                if step.step_number == step_number:
                    current_step = step
                    break

        if current_step:
            # Reset step status and attempts
            current_step.status = StepStatus.EXECUTING
            task.current_step_attempts = []

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "step_retry",
                    "content": f"Retrying step {step_number}",
                    "metadata": {
                        "stepNumber": step_number,
                        "category": "step_management"
                    }
                }),
                event="message"
            )

            # Retry the step from the beginning
            async for event in self._execute_single_step(task, current_step, start_attempt=1):
                yield event

    async def _handle_task_abort(self, task: TaskEntry) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle user request to abort the entire task."""
        print(f"[Orchestrator {task.id}] Aborting task")

        # Clear confirmation state
        task.awaiting_step_confirmation = False
        task.current_step_command = None
        task.current_step_description = None
        task.current_step_number = 0
        task.current_step_attempt = 1

        # Set task status
        task.status = "ABORTED"

        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_aborted",
                "content": "Task aborted by user",
                "metadata": {"category": "task_management"}
            }),
            event="message"
        )

        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_end",
                "content": "Task aborted by user",
                "metadata": {"category": "task_management"}
            }),
            event="end"
        )

    async def _trigger_error_recovery(self, task: TaskEntry, failed_step: TaskStep,
                                    failed_command: str, ssh_result: SSHResult) -> AsyncGenerator[ServerSentEvent, None]:
        """Trigger error recovery planning and execution."""
        print(f"[Orchestrator {task.id}] Starting error recovery for step {failed_step.step_number}")

        yield ServerSentEvent(
            data=json.dumps({
                "type": "error_recovery_start",
                "content": f"Command failed, creating recovery plan...",
                "metadata": {
                    "stepNumber": failed_step.step_number,
                    "failedCommand": failed_command,
                    "category": "error_recovery"
                }
            }),
            event="message"
        )

        try:
            # Create error recovery plan
            error_details = {
                "stdout": ssh_result.stdout,
                "stderr": ssh_result.stderr,
                "exit_status": ssh_result.exit_status
            }

            recovery_response = await self.error_recovery_planner.process(
                failed_command=failed_command,
                error_details=error_details,
                original_objective=failed_step.description
            )

            if not recovery_response.success or not recovery_response.recovery_plan:
                print(f"[Orchestrator {task.id}] Error recovery planning failed")
                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "error_recovery_failed",
                        "content": "Could not create recovery plan. Manual intervention required.",
                        "metadata": {"category": "error_recovery"}
                    }),
                    event="error"
                )
                failed_step.status = StepStatus.FAILED
                return

            print(f"[Orchestrator {task.id}] Created recovery plan with {len(recovery_response.recovery_plan)} steps")

            # Set up recovery state
            task.in_error_recovery = True
            task.recovery_plan = recovery_response.recovery_plan
            task.current_recovery_step = 0
            task.failed_step_number = failed_step.step_number

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error_recovery_plan",
                    "content": f"Created recovery plan with {len(recovery_response.recovery_plan)} steps",
                    "metadata": {
                        "recoverySteps": len(recovery_response.recovery_plan),
                        "recoveryApproach": recovery_response.recovery_approach,
                        "category": "error_recovery"
                    }
                }),
                event="message"
            )

            # Start executing the first recovery step
            async for event in self._execute_next_recovery_step(task, failed_step):
                yield event

        except Exception as e:
            error_msg = f"Error recovery failed: {str(e)}"
            print(f"[Orchestrator {task.id}] ERROR in recovery: {error_msg}")

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error_recovery_failed",
                    "content": error_msg,
                    "metadata": {"category": "error_recovery"}
                }),
                event="error"
            )

            failed_step.status = StepStatus.FAILED

    async def _execute_next_recovery_step(self, task: TaskEntry, failed_step: TaskStep) -> AsyncGenerator[ServerSentEvent, None]:
        """Execute the next recovery step in the recovery plan."""
        if not task.in_error_recovery or not task.recovery_plan:
            print(f"[Orchestrator {task.id}] No recovery plan available")
            return

        if task.current_recovery_step >= len(task.recovery_plan):
            # All recovery steps completed - try original step again
            print(f"[Orchestrator {task.id}] All recovery steps completed, retrying original step")
            await self._complete_error_recovery(task, failed_step)
            return

        recovery_step = task.recovery_plan[task.current_recovery_step]
        step_number = task.current_recovery_step + 1
        total_steps = len(task.recovery_plan)

        print(f"[Orchestrator {task.id}] Executing recovery step {step_number}/{total_steps}")

        yield ServerSentEvent(
            data=json.dumps({
                "type": "recovery_step_start",
                "content": f"Recovery Step {step_number}: {recovery_step['description']}",
                "metadata": {
                    "recoveryStepNumber": step_number,
                    "totalRecoverySteps": total_steps,
                    "category": "error_recovery"
                }
            }),
            event="message"
        )

        # Request confirmation for recovery command
        recovery_command = recovery_step['command']

        # Set confirmation state for recovery
        task.awaiting_step_confirmation = True
        task.current_step_command = recovery_command
        task.current_step_description = f"Recovery: {recovery_step['description']}"
        task.current_step_number = failed_step.step_number  # Keep original step number
        task.status = "AWAITING_USER_CONFIRMATION"

        yield ServerSentEvent(
            data=json.dumps({
                "type": "command_confirmation",
                "content": recovery_command,
                "metadata": {
                    "category": "error_recovery",
                    "priority": "high",
                    "stepNumber": failed_step.step_number,
                    "recoveryStepNumber": step_number,
                    "stepDescription": f"Recovery: {recovery_step['description']}",
                    "securityRisk": self._assess_security_risk(recovery_command),
                    "isRecoveryCommand": True
                }
            }),
            event="message"
        )

    async def _complete_error_recovery(self, task: TaskEntry, failed_step: TaskStep) -> AsyncGenerator[ServerSentEvent, None]:
        """Complete error recovery and retry the original step."""
        print(f"[Orchestrator {task.id}] Error recovery completed, retrying original step")

        # CRITICAL FIX: Clear ALL recovery state and confirmation state
        task.in_error_recovery = False
        task.recovery_plan = []
        task.current_recovery_step = 0
        task.failed_step_number = 0

        # CRITICAL FIX: Clear any pending confirmation state
        task.awaiting_step_confirmation = False
        task.current_step_command = None
        task.current_step_description = None
        task.current_step_number = 0
        task.current_step_attempt = 1

        # Reset step for retry
        failed_step.status = StepStatus.EXECUTING
        task.current_step_attempts = []  # Clear previous attempts

        # CRITICAL FIX: Ensure task is in proper executing state
        task.status = "EXECUTING"

        yield ServerSentEvent(
            data=json.dumps({
                "type": "error_recovery_complete",
                "content": "Error recovery completed. Retrying original step...",
                "metadata": {
                    "stepNumber": failed_step.step_number,
                    "category": "error_recovery"
                }
            }),
            event="message"
        )

        print(f"[Orchestrator {task.id}] Starting retry of step {failed_step.step_number} after recovery")

        # Retry the original step from the beginning
        async for event in self._execute_single_step(task, failed_step, start_attempt=1):
            yield event

    async def _execute_recovery_command(self, task: TaskEntry, command: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Execute a recovery command and handle the result."""
        print(f"[Orchestrator {task.id}] Executing recovery command: {command}")

        # CRITICAL FIX: Ensure task is in proper executing state
        task.status = "EXECUTING"

        # Execute the command
        ssh_result = await self.ssh_client.execute_command_async(command)

        if ssh_result.exit_status == 0:
            # Recovery command succeeded
            print(f"[Orchestrator {task.id}] Recovery command succeeded")

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "recovery_step_success",
                    "content": f"Recovery step completed successfully",
                    "metadata": {
                        "recoveryStepNumber": task.current_recovery_step + 1,
                        "totalRecoverySteps": len(task.recovery_plan),
                        "category": "error_recovery"
                    }
                }),
                event="message"
            )

            # Move to next recovery step
            task.current_recovery_step += 1

            # Find the failed step
            failed_step = None
            if task.task_plan and task.task_plan.steps:
                for step in task.task_plan.steps:
                    if step.step_number == task.failed_step_number:
                        failed_step = step
                        break

            if failed_step:
                # CRITICAL FIX: Ensure we don't get stuck in recovery loops
                print(f"[Orchestrator {task.id}] Recovery step {task.current_recovery_step}/{len(task.recovery_plan)} completed")

                # Continue with next recovery step or complete recovery
                async for event in self._execute_next_recovery_step(task, failed_step):
                    yield event
            else:
                print(f"[Orchestrator {task.id}] WARNING: Could not find failed step {task.failed_step_number}")
                # Force complete recovery if step not found
                task.in_error_recovery = False
                task.recovery_plan = []
                task.current_recovery_step = 0
                task.failed_step_number = 0
                task.status = "EXECUTING"
        else:
            # Recovery command failed
            print(f"[Orchestrator {task.id}] Recovery command failed: {ssh_result.stderr}")

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "recovery_step_failed",
                    "content": f"Recovery step failed: {ssh_result.stderr}",
                    "metadata": {
                        "recoveryStepNumber": task.current_recovery_step + 1,
                        "totalRecoverySteps": len(task.recovery_plan),
                        "category": "error_recovery"
                    }
                }),
                event="message"
            )

            # Recovery failed - mark the original step as failed
            if task.task_plan and task.task_plan.steps:
                for step in task.task_plan.steps:
                    if step.step_number == task.failed_step_number:
                        step.status = StepStatus.FAILED
                        break

            # Clear recovery state
            task.in_error_recovery = False
            task.recovery_plan = []
            task.current_recovery_step = 0
            task.failed_step_number = 0
            task.status = "FAILED"

            yield ServerSentEvent(
                data=json.dumps({
                    "type": "task_failed",
                    "content": "Error recovery failed. Task cannot continue.",
                    "metadata": {"category": "task_failure"}
                }),
                event="error"
            )